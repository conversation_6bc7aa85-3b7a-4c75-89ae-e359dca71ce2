// src/services/generationService.js
import { nhost } from './nhost';
import { gql } from '@apollo/client';
import axios from 'axios'; // For ComfyUI direct interactions
import { CreditService } from './creditService';

// --- Design Decision Documentation ---
// (Retained: Two-table approach. Prompts logs attempts, Projects stores saved work)
// Key Storage Changes for this version:
// 1. User's original input file:
//    - Uploaded to Nhost `user_inputs` bucket using Nhost SDK for record-keeping.
//      The URL of this Nhost-stored input is saved in `projects.input_file_url`.
//    - Uploaded *directly* to ComfyUI for processing.
// 2. ComfyUI's output files:
//    - Fetched as blobs.
//    - Uploaded to Nhost `user_outputs` bucket using Nhost SDK.
//      URLs are stored in `projects.outputs`.
// --- End Design Decision Documentation ---

// --- Custom Error Classes ---
class GenerationServiceError extends Error { constructor(message, originalError, details) { super(message); this.name = 'GenerationServiceError'; this.originalError = originalError; this.details = details; }}
class GraphQLServiceError extends GenerationServiceError { constructor(message, graphqlErrors, originalError) { const gqlMessage = graphqlErrors?.[0]?.message || message; super(`GraphQL operation failed: ${gqlMessage}`, originalError); this.name = 'GraphQLServiceError'; this.graphqlErrors = graphqlErrors; }}
class ComfyExecutionError extends GenerationServiceError { constructor(message, promptId, historyData = null) { super(message); this.name = 'ComfyExecutionError'; this.promptId = promptId; this.historyData = historyData; }}
class NhostStorageError extends GenerationServiceError { constructor(message, originalError, operation) { super(message); this.name = 'NhostStorageError'; this.originalError = originalError; this.operation = operation; }}

// --- Configuration ---
const COMFY_INSTANCE_CACHE = {};
const USER_INPUTS_BUCKET_ID = 'user_inputs';
const USER_OUTPUTS_BUCKET_ID = 'user_outputs';
const SAUCES_BUCKET_ID = 'sauces'; // For workflow templates
const MAX_INPUT_FILE_SIZE_BYTES = 10 * 1024 * 1024; // 10MB
const ALLOWED_INPUT_MIME_TYPES = ['image/jpeg', 'image/png', 'image/webp', 'image/gif'];

// --- GraphQL Definitions ---
const GET_COMFY_SERVER_DETAILS_QUERY = gql` query GetComfyServerDetails($toolId: String!, $serverType: String!) { comfyui_servers( where: { _or: [{tool_id: {_eq: $toolId}}, {tool_id: {_eq: "all"}}], is_active: {_eq: true}, server_type: {_eq: $serverType} }, limit: 1, order_by: {queue_priority: asc}) { id url server_type cost_credits_per_second } } `;
const GET_TOOL_WORKFLOW_QUERY = gql` query GetToolWorkflow($toolId: String!) { tools_by_pk(id: $toolId) { id workflow_file_id } } `;

// Health Check Queries - Optimized for essential checks only
const HEALTH_CHECK_COMFY_SERVERS_QUERY = gql`
  query HealthCheckComfyServers($toolId: String!, $serverType: String!) {
    comfyui_servers(
      where: {
        _or: [{tool_id: {_eq: $toolId}}, {tool_id: {_eq: "all"}}],
        is_active: {_eq: true},
        server_type: {_eq: $serverType}
      }
    ) {
      id
      url
      server_type
      is_active
      tool_id
    }
  }
`;

const HEALTH_CHECK_TOOLS_QUERY = gql`
  query HealthCheckTools($toolId: String!) {
    tools_by_pk(id: $toolId) {
      id
      name
      workflow_file_id
      is_active
    }
  }
`;



const INSERT_PROMPT_LOG_MUTATION = gql`
  mutation InsertPromptLog(
      $userId: uuid!,
      $toolId: String!,
      $inputFile: String,
      $userPrompt: String,
      $serverType: String!,
      $status: String!,
      $uploadedFileId: uuid
    ) {
    insert_prompts_one(object: {
        user_id: $userId,
        tool_id: $toolId,
        input_file: $inputFile,
        user_prompt: $userPrompt,
        server_type: $serverType,
        status: $status,
        uploaded_file_id: $uploadedFileId
     }) {
      id
      user_id
      tool_id
      input_file
      user_prompt
      server_type
      status
      uploaded_file_id
      created_at
      updated_at
    }
  }
`;
const UPDATE_PROMPT_STATUS_MUTATION = gql`
  mutation UpdatePromptStatus($promptLogId: uuid!, $status: String!, $errorMessage: String) {
    update_prompts_by_pk(pk_columns: {id: $promptLogId}, _set: {status: $status, error_message: $errorMessage}) {
      id
      user_id
      tool_id
      status
      error_message
      updated_at
    }
  }
`;

const UPDATE_PROMPT_PROJECT_MUTATION = gql`
  mutation UpdatePromptProject($promptLogId: uuid!, $projectId: uuid!) {
    update_prompts_by_pk(pk_columns: {id: $promptLogId}, _set: {project_id: $projectId}) {
      id
      project_id
      updated_at
    }
  }
`;

const INSERT_PROJECT_MUTATION = gql`
  mutation InsertProject($userId: uuid!, $promptId: uuid, $name: String, $inputFileUrl: String, $promptInputText: String, $creditCost: numeric, $generationDurationMs: Int, $outputs: jsonb) {
    insert_projects_one(object: {
      user_id: $userId,
      prompt_id: $promptId,
      name: $name,
      input_file_url: $inputFileUrl,
      prompt_input_text: $promptInputText,
      credit_cost: $creditCost,
      generation_duration_ms: $generationDurationMs,
      outputs: $outputs
    }) {
      id
      created_at
      saved_at
    }
  }
`;


// --- Helper Function for Nhost Storage Upload (using Nhost SDK) ---
async function uploadFileToNhostStorageWithSDK(file, bucketId, desiredName) {
    if (!file) throw new NhostStorageError("File object is required for upload.", null, "uploadValidation");

    if (bucketId === USER_INPUTS_BUCKET_ID) {
        if (!ALLOWED_INPUT_MIME_TYPES.includes(file.type)) {
            throw new NhostStorageError(`Invalid file type: ${file.type}. Allowed: ${ALLOWED_INPUT_MIME_TYPES.join(', ')}`, null, "uploadValidation");
        }
        if (file.size > MAX_INPUT_FILE_SIZE_BYTES) {
            throw new NhostStorageError(`File size (${(file.size / 1024 / 1024).toFixed(2)}MB) exceeds ${MAX_INPUT_FILE_SIZE_BYTES / 1024 / 1024}MB limit.`, null, "uploadValidation");
        }
    }

    console.log(`[GenService-NhostSDK] Uploading '${file.name}' to bucket '${bucketId}' as '${desiredName || file.name}'`);
    try {
        const { fileMetadata, error } = await nhost.storage.upload({
            bucketId: bucketId,
            file: file,
            name: desiredName || file.name,
            contentType: file.type,
        });

        if (error) {
            console.error("[GenService-NhostSDK] Nhost SDK storage.upload error:", error);
            throw new NhostStorageError(`Nhost SDK Upload Error: ${error.message} (Status: ${error.status || 'N/A'})`, error, "uploadSdk");
        }
        if (!fileMetadata || !fileMetadata.id) {
            throw new NhostStorageError("Nhost SDK upload did not return expected file metadata.", null, "uploadSdk");
        }
        
        const { presignedUrl, error: urlError } = await nhost.storage.getPresignedUrl({ fileId: fileMetadata.id });
        
        if (urlError || !presignedUrl?.url) {
            // This is a critical failure if the client needs to access this URL immediately
            // or if the URL is being sent to another service that needs public access.
            console.error(`[GenService-NhostSDK] CRITICAL: Failed to get presigned URL for ${fileMetadata.id}: ${urlError?.message}. File may be inaccessible.`);
            throw new NhostStorageError(`Failed to get accessible URL for uploaded file ${fileMetadata.name}: ${urlError?.message || 'Presigned URL missing'}`, urlError, "presignedUrl");
        }

        console.log(`[GenService-NhostSDK] File uploaded. ID: ${fileMetadata.id}, Name: ${fileMetadata.name}, Presigned URL: ${presignedUrl.url.substring(0,100)}...`);
        return {
            id: fileMetadata.id,
            name: fileMetadata.name,
            url: presignedUrl.url, 
            bucketId: fileMetadata.bucketId || bucketId,
            size: fileMetadata.size,
            mimeType: fileMetadata.mimeType,
        };

    } catch (error) {
        if (error instanceof NhostStorageError) throw error;
        const errMsg = error.message || "Unknown Nhost SDK upload error";
        console.error("[GenService-NhostSDK] Error during Nhost SDK upload:", errMsg, error);
        throw new NhostStorageError(`Nhost SDK Upload Error: ${errMsg}`, error, "uploadSdkCatchAll");
    }
}

// --- Health Check Function ---
async function performHealthCheck(toolId, serverType) {
    console.log(`[GenService] 🏥 Starting optimized health check for tool ${toolId}, server type ${serverType}`);

    const healthResults = {
        databaseAccess: { passed: false, details: {} },
        toolValidation: { passed: false, details: {} },
        activeServers: { passed: false, details: {} },
        overall: false
    };

    try {
        // 1. Validate tool exists and is active (this also serves as database connectivity check)
        console.log(`[GenService] 🔧 Validating tool ${toolId} and database connectivity...`);

        const { data: toolData, error: toolError } = await nhost.graphql.request(HEALTH_CHECK_TOOLS_QUERY, { toolId });

        if (toolError) {
            console.error(`[GenService] ❌ Database connectivity or tool validation failed:`, toolError);
            healthResults.databaseAccess.details = { error: toolError.message };
            healthResults.toolValidation.details = { error: toolError.message };
            throw new GenerationServiceError("Database connectivity or tool validation failed.");
        }

        // Database connectivity is confirmed if we got here
        healthResults.databaseAccess.passed = true;
        healthResults.databaseAccess.details = { connectivity: 'verified via tool query' };
        console.log(`[GenService] ✅ Database connectivity verified`);

        const tool = toolData?.tools_by_pk;
        if (!tool) {
            console.error(`[GenService] ❌ Tool '${toolId}' not found`);
            healthResults.toolValidation.details = { error: `Tool '${toolId}' not found` };
            throw new GenerationServiceError(`Tool '${toolId}' does not exist.`);
        }

        if (!tool.is_active) {
            console.error(`[GenService] ❌ Tool '${toolId}' is not active`);
            healthResults.toolValidation.details = { error: `Tool '${toolId}' is not active` };
            throw new GenerationServiceError(`Tool '${toolId}' is not active.`);
        }

        healthResults.toolValidation.passed = true;
        healthResults.toolValidation.details = {
            toolId: tool.id,
            name: tool.name,
            isActive: tool.is_active,
            hasWorkflow: !!tool.workflow_file_id
        };
        console.log(`[GenService] ✅ Tool '${toolId}' validated successfully`);

        // 3. Check if at least one ComfyUI server is active (unchanged)
        console.log(`[GenService] 🖥️ Checking active ComfyUI servers for tool ${toolId}, server type ${serverType}...`);

        const { data: serverData, error: serverError } = await nhost.graphql.request(HEALTH_CHECK_COMFY_SERVERS_QUERY, { toolId, serverType });

        if (serverError) {
            console.error(`[GenService] ❌ Failed to query ComfyUI servers:`, serverError);
            healthResults.activeServers.details = { error: serverError.message };
            throw new GenerationServiceError("Failed to check ComfyUI server availability.");
        }

        const activeServers = serverData?.comfyui_servers || [];
        healthResults.activeServers.details = {
            serversFound: activeServers.length,
            servers: activeServers.map(s => ({
                id: s.id,
                url: s.url,
                server_type: s.server_type,
                tool_id: s.tool_id
            }))
        };

        if (activeServers.length === 0) {
            console.error(`[GenService] ❌ No active ComfyUI servers found for tool ${toolId}, server type ${serverType}`);
            throw new GenerationServiceError(`No active ComfyUI servers found for tool '${toolId}' with server type '${serverType}'.`);
        }

        console.log(`[GenService] ✅ Found ${activeServers.length} active ComfyUI server(s) for tool ${toolId}`);
        healthResults.activeServers.passed = true;

        // All checks passed
        healthResults.overall = true;
        console.log(`[GenService] 🎉 Optimized health check passed for tool ${toolId}`);

        return healthResults;

    } catch (error) {
        console.error(`[GenService] 💥 Health check failed:`, error.message);
        healthResults.overall = false;
        throw error;
    }
}

// --- Core Service Helper Functions ---
async function getComfyInstanceDetails(toolId, serverType) {
    const cacheKey = `${toolId}_${serverType}`;
    if (COMFY_INSTANCE_CACHE[cacheKey]) return COMFY_INSTANCE_CACHE[cacheKey];
    try {
        const { data, error } = await nhost.graphql.request(GET_COMFY_SERVER_DETAILS_QUERY, { toolId, serverType });
        if (error) throw new GraphQLServiceError("Failed to get ComfyUI server details", error.response?.errors, error);
        const server = data?.comfyui_servers?.[0];
        if (!server?.url) throw new GenerationServiceError(`Active ComfyUI server URL for tool '${toolId}' type '${serverType}' not found.`);
        const costRate = server.cost_credits_per_second;
        const validCostRate = (typeof costRate === 'number' && costRate >= 0) ? Number(costRate) : 0;
        const details = { url: server.url, server_type: server.server_type, cost_credits_per_second: validCostRate };
        COMFY_INSTANCE_CACHE[cacheKey] = details;
        return details;
    } catch (err) {
        if (err instanceof GenerationServiceError || err instanceof GraphQLServiceError) throw err;
        throw new GenerationServiceError(`Could not get Comfy details: ${err.message}`, err);
    }
}

async function getToolWorkflowId(toolId) {
    try {
        const { data, error } = await nhost.graphql.request(GET_TOOL_WORKFLOW_QUERY, { toolId });
        if (error) throw new GraphQLServiceError("Failed to get tool workflow ID", error.response?.errors, error);
        const workflowFileId = data?.tools_by_pk?.workflow_file_id;
        if (!workflowFileId) throw new GenerationServiceError(`Workflow File ID not found for tool '${toolId}'.`);
        return workflowFileId;
    } catch (err) {
        if (err instanceof GenerationServiceError || err instanceof GraphQLServiceError) throw err;
        throw new GenerationServiceError(`Could not get tool workflow ID: ${err.message}`, err);
    }
}

async function determineServerType(userId, toolId) {
    try {
        if (!CreditService?.getUserWalletInfo) throw new GenerationServiceError("CreditService not available.");
        const walletInfo = await CreditService.getUserWalletInfo(userId);

        // Check if user has an active pass with unlimited usage
        if (walletInfo.hasUnlimitedUsage) {
            return 'fast'; // Unlimited users get fast by default
        }

        // Check fast credits balance
        const hasFastCredits = (walletInfo.fastCredits ?? 0) > 0;
        if (hasFastCredits) {
            return 'fast';
        }

        // Default to slow
        return 'slow';
    } catch (err) {
        console.warn(`[GenService] Could not determine server type for user ${userId}, tool ${toolId}: ${err.message}. Defaulting to slow.`);
        return 'slow'; // Safe fallback
    }
}

async function updatePromptLogStatus(promptLogId, status, errorMessage = null) {
    if (!promptLogId) { console.warn('[GenService] updatePromptLogStatus: no promptLogId.'); return; }
    console.log(`[GenService] Updating prompt ${promptLogId} to ${status}` + (errorMessage ? ` | Error: ${String(errorMessage).substring(0,50)}...` : ""));
    try {
        const { data, error } = await nhost.graphql.request(UPDATE_PROMPT_STATUS_MUTATION, { promptLogId, status, errorMessage: errorMessage ? String(errorMessage).substring(0, 2000) : null });
        if (error) {
            console.error(`[GenService] GQL Error updating prompt ${promptLogId} status:`, JSON.stringify(error, null, 2));
            // Check if the error is "field not found in mutation_root"
            if (error.message && error.message.includes("not found in type: 'mutation_root'")) {
                console.error("[GenService] CRITICAL: 'update_prompts_by_pk' mutation not accessible. Check Hasura permissions for 'prompts' table 'update' operation for the 'user' role. Ensure 'backend_only' is false.");
            }
        } else if (data?.update_prompts_by_pk) {
            console.log(`[GenService] Prompt ${promptLogId} status updated to ${data.update_prompts_by_pk.status}`);
        } else {
            console.warn(`[GenService] Prompt status update for ${promptLogId} did not return expected data (e.g. status field). Response:`, data);
        }
    } catch (err) { console.error(`[GenService] Unexpected error updating prompt ${promptLogId} status:`, err); }
}

async function getWorkflowTemplate(workflowFileId) {
    if (!workflowFileId) throw new GenerationServiceError("Workflow File ID is invalid.");

    console.log(`[GenService] Loading workflow template from file ID: ${workflowFileId} (expected bucket: ${SAUCES_BUCKET_ID})`);

    try {
        const { presignedUrl, error } = await nhost.storage.getPresignedUrl({ fileId: workflowFileId });
        if (error) {
            console.error(`[GenService] Failed to get workflow presigned URL for ${workflowFileId}:`, error);
            throw new NhostStorageError(`Failed to get workflow presigned URL: ${error.message}`, error, "presignedUrl");
        }
        if (!presignedUrl?.url) {
            console.error(`[GenService] Presigned URL missing for workflow file ${workflowFileId}`);
            throw new NhostStorageError('Presigned URL for workflow is missing.', null, "presignedUrl");
        }

        console.log(`[GenService] Downloading workflow from: ${presignedUrl.url.substring(0, 100)}...`);
        const response = await fetch(presignedUrl.url);
        if (!response.ok) {
            console.error(`[GenService] Workflow download failed for ${workflowFileId}: ${response.status} ${response.statusText}`);
            throw new GenerationServiceError(`Workflow download failed: ${response.statusText}`);
        }

        const workflowData = await response.json();
        console.log(`[GenService] Workflow template loaded successfully from ${workflowFileId}`);
        return workflowData;
    } catch (error) {
        if (error instanceof NhostStorageError || error instanceof GenerationServiceError) throw error;
        console.error(`[GenService] Unexpected error loading workflow ${workflowFileId}:`, error);
        throw new GenerationServiceError(`Could not load workflow: ${error.message}`, error);
    }
}

async function uploadInputToComfyDirect(file, comfyInstanceUrl) {
    if (!file) throw new GenerationServiceError("Input file is required for ComfyUI direct upload.");
    if (!comfyInstanceUrl) throw new GenerationServiceError("ComfyUI instance URL is required.");
    const formData = new FormData();
    formData.append('image', file, file.name);
    const uploadUrl = `${comfyInstanceUrl}/upload/image`;
    console.log(`[GenService-ComfyDirect] Uploading '${file.name}' to Comfy: ${uploadUrl}`);
    try {
        const response = await axios.post(uploadUrl, formData, { timeout: 90000 });
        const comfyFileData = response.data;
        if (!comfyFileData?.name) {
            throw new GenerationServiceError('ComfyUI direct upload response invalid (filename missing).', null, comfyFileData);
        }
        console.log(`[GenService-ComfyDirect] Input uploaded to Comfy. Path: ${comfyFileData.subfolder ? comfyFileData.subfolder + '/' : ''}${comfyFileData.name}`);
        return { name: comfyFileData.name, subfolder: comfyFileData.subfolder, type: comfyFileData.type };
    } catch (error) {
        const errMsg = error.response?.data?.error || error.message || 'Unknown ComfyUI direct upload error';
        console.error(`[GenService-ComfyDirect] Upload Error:`, errMsg, error.response?.data);
        throw new GenerationServiceError(`ComfyUI Direct Upload Failed: ${errMsg}`, error);
    }
}

async function submitWorkflowToComfy(workflowPayload, comfyInstanceUrl, comfyInputFileName, inputImageNodeId, promptText, inputPromptNodeId, promptInputKey) {
    if (!workflowPayload || !comfyInstanceUrl || !comfyInputFileName || !inputImageNodeId) {
        throw new GenerationServiceError("Missing required parameters for ComfyUI submission (direct input).");
    }
    if (!workflowPayload[inputImageNodeId]) workflowPayload[inputImageNodeId] = { inputs: {} };
    workflowPayload[inputImageNodeId].inputs.image = comfyInputFileName;

    if (inputPromptNodeId && workflowPayload[inputPromptNodeId] && promptText) {
        if(!workflowPayload[inputPromptNodeId].inputs) workflowPayload[inputPromptNodeId].inputs = {};
        workflowPayload[inputPromptNodeId].inputs[promptInputKey] = promptText;
    } else if (inputPromptNodeId && promptText) {
         console.warn(`[GenService] Prompt node ${inputPromptNodeId} not found in workflow, but prompt text was provided. Prompt not set in payload.`);
    }

    const submitUrl = `${comfyInstanceUrl}/prompt`;
    console.log(`[GenService] Submitting workflow to Comfy: ${submitUrl} with Comfy input file: ${comfyInputFileName}`);
    try {
        const response = await axios.post(submitUrl, { prompt: workflowPayload }, { headers: { 'Content-Type': 'application/json' }, timeout: 30000 });
        const comfyPromptId = response.data?.prompt_id;
        if (!comfyPromptId) throw new GenerationServiceError('ComfyUI submit response invalid (prompt_id missing).', null, response.data);
        console.log(`[GenService] Workflow submitted to Comfy. Internal Prompt ID: ${comfyPromptId}`);
        return comfyPromptId;
    } catch (error) {
        const errMsg = error.response?.data?.error || error.message || 'Unknown ComfyUI submission error';
        console.error(`[GenService] ComfyUI Submit Error:`, errMsg, error.response?.data);
        throw new GenerationServiceError(`ComfyUI workflow submission failed: ${errMsg}`, error);
    }
}

async function pollComfyOutputAndUploadToNhost(comfySubmittedPromptId, comfyInstanceUrl, userId, toolId, promptLogIdForNaming) {
    const startTime = Date.now();
    if (!comfySubmittedPromptId || !comfyInstanceUrl || !userId || !toolId) {
        throw new GenerationServiceError("Missing parameters for polling ComfyUI output.");
    }
    const historyUrl = `${comfyInstanceUrl}/history/${comfySubmittedPromptId}`;
    console.log(`[GenService-Poll] Polling ComfyUI prompt: ${comfySubmittedPromptId} at ${historyUrl}`);
    let attempt = 0; const maxAttempts = 120; const pollInterval = 3000;
    let processedOutputsMetadata = [];

    while (attempt < maxAttempts) {
        attempt++;
        try {
            await new Promise((resolve) => setTimeout(resolve, pollInterval));
            const historyResponse = await axios.get(historyUrl, { timeout: 20000 });
            const historyData = historyResponse.data;
            const promptHistory = historyData?.[comfySubmittedPromptId];

            if (!promptHistory) { 
                console.warn(`[GenService-Poll] Attempt ${attempt}/${maxAttempts}: Prompt ID ${comfySubmittedPromptId} not found in history. Retrying...`);
                if (attempt > 10 && attempt % 10 === 0) console.warn(`[GenService-Poll] Still no history for ${comfySubmittedPromptId} after ${attempt} attempts.`);
                continue;
            }
            if (promptHistory?.status?.status_str === 'error' || promptHistory?.status?.error) {
                let extractedErrorMessage = promptHistory.status?.exception_message || 'ComfyUI execution failed.';
                throw new ComfyExecutionError(extractedErrorMessage, comfySubmittedPromptId, promptHistory);
            }

            if (promptHistory?.outputs && Object.keys(promptHistory.outputs).length > 0) {
                for (const nodeId in promptHistory.outputs) {
                    const nodeOutput = promptHistory.outputs[nodeId];
                    const resultsArray = nodeOutput.images || nodeOutput.gifs || nodeOutput.videos;
                    if (resultsArray && Array.isArray(resultsArray)) {
                        for (const result of resultsArray) {
                            if (result?.filename) {
                                const comfyOutputFileName = result.filename;
                                const outputTypeComfy = result.type || 'output';
                                const subfolder = result.subfolder || '';
                                const comfyViewUrl = `${comfyInstanceUrl}/view?filename=${encodeURIComponent(comfyOutputFileName)}&type=${outputTypeComfy}&subfolder=${encodeURIComponent(subfolder)}`;
                                
                                console.log(`[GenService-Poll] Fetching output: ${comfyOutputFileName} from ${comfyViewUrl.substring(0,100)}...`);
                                const imageResponse = await axios.get(comfyViewUrl, { responseType: 'blob', timeout: 60000 });
                                const outputBlob = imageResponse.data;
                                
                                const safeOriginalName = comfyOutputFileName.replace(/[^a-zA-Z0-9._-]/g, '_');
                                const namedOutputBlob = new File([outputBlob], safeOriginalName, { type: outputBlob.type });

                                const uniqueOutputNameInNhost = `${userId}/${toolId}/outputs/${promptLogIdForNaming || comfySubmittedPromptId}_${Date.now()}_${safeOriginalName}`;
                                
                                const nhostOutputMetadata = await uploadFileToNhostStorageWithSDK(namedOutputBlob, USER_OUTPUTS_BUCKET_ID, uniqueOutputNameInNhost);
                                processedOutputsMetadata.push(nhostOutputMetadata);
                                console.log(`[GenService-Poll] Output '${comfyOutputFileName}' uploaded to Nhost as '${nhostOutputMetadata.name}'.`);
                            }
                        }
                    }
                }
                if (processedOutputsMetadata.length > 0) {
                    console.log(`[GenService-Poll] Polling success for ${comfySubmittedPromptId}. ${processedOutputsMetadata.length} output(s) processed.`);
                    return { outputs: processedOutputsMetadata, durationMs: Date.now() - startTime };
                } else {
                     console.log(`[GenService-Poll] Attempt ${attempt}: Outputs structure found for ${comfySubmittedPromptId}, but no processable files yet.`);
                }
            } else { 
                console.log(`[GenService-Poll] Attempt ${attempt}/${maxAttempts} for ${comfySubmittedPromptId}: Not ready (no outputs object or empty).`);
            }
        } catch (error) { 
            if (error instanceof ComfyExecutionError || error instanceof NhostStorageError) throw error;
            console.error(`[GenService-Poll] Polling attempt ${attempt} for ${comfySubmittedPromptId} failed:`, error.message);
            if (attempt >= maxAttempts) throw new GenerationServiceError(`Polling timed out. Last error: ${error.message}`, error);
        }
    }
    throw new GenerationServiceError(`Polling timed out for ${comfySubmittedPromptId}. No valid outputs found.`);
}

async function logPromptAttempt(userId, toolId, inputFileName, userPromptText, serverType, status = 'pending', uploadedFileId = null) {
    if (!userId) { console.warn('[GenService] logPromptAttempt: userId missing.'); return null; }
    if (!toolId) { console.warn('[GenService] logPromptAttempt: toolId missing.'); return null; }

    // Validate server type
    const validServerTypes = ['slow', 'fast'];
    if (!validServerTypes.includes(serverType)) {
        console.warn(`[GenService] Invalid server type '${serverType}', defaulting to 'slow'`);
        serverType = 'slow';
    }

    // Validate status
    const validStatuses = ['pending', 'processing', 'completed', 'failed'];
    if (!validStatuses.includes(status)) {
        console.warn(`[GenService] Invalid status '${status}', defaulting to 'pending'`);
        status = 'pending';
    }

    const variables = {
        userId,
        toolId,
        inputFile: inputFileName || null,
        userPrompt: userPromptText || null,
        serverType,
        status,
        uploadedFileId: uploadedFileId
    };

    console.log('[GenService] 📝 Logging prompt attempt with variables:', variables);

    try {
        const { data, error } = await nhost.graphql.request(INSERT_PROMPT_LOG_MUTATION, variables);
        if (error) {
            console.error('[GenService] ❌ GQL error logging prompt:', JSON.stringify(error, null, 2));

            // Check for specific error types
            if (error.message && error.message.includes("not found in type: 'prompts_insert_input'")) {
                console.error("[GenService] CRITICAL: Mismatch between INSERT_PROMPT_LOG_MUTATION and 'prompts' table schema. Check GQL definition and Hasura table columns/permissions.");
            } else if (error.message && error.message.includes("Foreign key violation")) {
                console.error(`[GenService] CRITICAL: Foreign key violation - tool_id '${toolId}' may not exist in tools table.`);
            } else if (error.message && error.message.includes("permission denied")) {
                console.error("[GenService] CRITICAL: Permission denied for prompts table insert. Check Hasura permissions for 'user' role.");
            }
            return null;
        }

        const logId = data?.insert_prompts_one?.id;
        const createdAt = data?.insert_prompts_one?.created_at;
        if (logId) {
            console.log(`[GenService] ✅ Prompt log saved via GraphQL, ID: ${logId}, Created: ${createdAt}`);
        } else {
            console.warn('[GenService] ⚠️ Prompt log mutation succeeded but returned no ID.');
        }
        return logId;
    } catch (err) {
        console.error('[GenService] 💥 Unexpected error logging prompt:', err);
        return null;
    }
}
async function calculateCreditCost(durationMs, serverType, toolId) {
  try {
    // Get the tool's credit cost rates
    const { data, error } = await nhost.graphql.request(`
      query GetToolCreditRates($toolId: String!) {
        tools_by_pk(id: $toolId) {
          slow_credit_cost_per_second
          fast_credit_cost_per_second
        }
      }
    `, { toolId });
    
    if (error) {
      console.error(`[GenService] Error fetching tool credit rates:`, error);
      // Default fallback rates if query fails
      return serverType === 'fast' ? Math.ceil(durationMs / 1000 * 0.5) : Math.ceil(durationMs / 1000 * 0.1);
    }
    
    const tool = data?.tools_by_pk;
    if (!tool) {
      console.warn(`[GenService] Tool ${toolId} not found, using default rates`);
      return serverType === 'fast' ? Math.ceil(durationMs / 1000 * 0.5) : Math.ceil(durationMs / 1000 * 0.1);
    }
    
    // Calculate cost based on duration and appropriate rate
    const ratePerSecond = serverType === 'fast' 
      ? tool.fast_credit_cost_per_second 
      : tool.slow_credit_cost_per_second;
    
    const durationSeconds = durationMs / 1000;
    const cost = Math.ceil(durationSeconds * ratePerSecond);
    
    console.log(`[GenService] Calculated credit cost: ${cost} (${durationSeconds}s at ${ratePerSecond}/s)`);
    return cost;
  } catch (error) {
    console.error(`[GenService] Error calculating credit cost:`, error);
    // Fallback calculation
    return serverType === 'fast' ? Math.ceil(durationMs / 1000 * 0.5) : Math.ceil(durationMs / 1000 * 0.1);
  }
}

// --- Main Generation Function ---
async function runGenerationJob(
    userId, toolId, inputFileObject, userPromptText,
    comfyInputImageNodeId, comfyInputPromptNodeId, comfyPromptInputKey = 'text'
) {
    console.log(`[GenService] RunJob V11 (Health Check + Enhanced Logging): User ${userId}, Tool ${toolId}, File ${inputFileObject?.name}`);
    let promptLogId = null;
    let nhostUploadedInputInfo = null;
    let serverTypeActuallyUsed = 'slow';
    let comfyDirectInputInfo = null;
    let healthCheckResults = null;

    try {
        // Enhanced user session validation
        const user = nhost.auth.getUser();
        const session = nhost.auth.getSession();

        console.log(`[GenService] 🔐 Validating user session for ${userId}`);
        if (!user || !session) {
            console.error(`[GenService] ❌ No valid user session found. User: ${!!user}, Session: ${!!session}`);
            throw new GenerationServiceError("User session expired, please log in again.");
        }

        if (user.id !== userId) {
            console.error(`[GenService] ❌ User ID mismatch. Expected: ${userId}, Got: ${user.id}`);
            throw new GenerationServiceError("User session expired, please log in again.");
        }

        if (!toolId || !inputFileObject) {
            throw new GenerationServiceError("Tool ID and Input File are required.");
        }

        console.log(`[GenService] ✅ Session validation passed for user ${userId}`);

        // Determine server type first (needed for health check)
        const serverTypeDetermined = await determineServerType(userId, toolId);
        serverTypeActuallyUsed = serverTypeDetermined;

        console.log(`[GenService] 🎯 Server type determined: ${serverTypeDetermined}`);

        // Perform comprehensive health check before any file operations
        // This now includes tool validation, database connectivity, and server availability
        console.log(`[GenService] 🏥 Performing comprehensive health check before proceeding...`);
        healthCheckResults = await performHealthCheck(toolId, serverTypeDetermined);

        console.log(`[GenService] ✅ Health check passed:`, {
            databaseAccess: healthCheckResults.databaseAccess.passed,
            toolValidation: healthCheckResults.toolValidation.passed,
            activeServers: healthCheckResults.activeServers.passed,
            serversFound: healthCheckResults.activeServers.details.serversFound
        });

        // Temporarily bypass credit check (as requested for development)
        console.warn(`[GenService] ⚠️ Credit check bypassed for development, to be implemented in Phase 2`);
        // COMMENTED OUT FOR DEVELOPMENT - TO BE RESTORED IN PHASE 2:
        /*
        const balanceToCheck = serverTypeDetermined === 'fast' ? (walletInfo.fastCreditsRemaining ?? 0) : (walletInfo.credits ?? 0);
        const planAllowsOverdraft = walletInfo.plan?.allows_overdraft ?? false;
        if (balanceToCheck <= 0 && !(planAllowsOverdraft && serverTypeDetermined !== 'fast')) {
            await updatePromptLogStatus(promptLogId, 'credit_error', `Insufficient ${serverTypeDetermined} credits.`);
            throw new GenerationServiceError(`Insufficient ${serverTypeDetermined} credits.`);
        }
        */

        // 1. Upload original input file to Nhost `user_inputs` bucket (only if health check passed)
        console.log(`[GenService] 📤 Starting file upload operations...`);
        const uniqueInputNameInNhost = `${userId}/${toolId}/inputs/${Date.now()}_${inputFileObject.name.replace(/\s+/g, '_')}`;

        try {
            console.log(`[GenService] 📁 Attempting to upload input file to Nhost bucket: ${USER_INPUTS_BUCKET_ID}`);
            nhostUploadedInputInfo = await uploadFileToNhostStorageWithSDK(inputFileObject, USER_INPUTS_BUCKET_ID, uniqueInputNameInNhost);
            console.log(`[GenService] ✅ User input file uploaded to Nhost user_inputs bucket. File ID: ${nhostUploadedInputInfo.id}, Name: ${nhostUploadedInputInfo.name}`);
        } catch (nhostUploadError) {
            console.error(`[GenService] ❌ Nhost upload failed for ${inputFileObject.name}:`, {
                error: nhostUploadError.message,
                status: nhostUploadError.originalError?.status || 'N/A',
                operation: nhostUploadError.operation || 'upload',
                bucketId: USER_INPUTS_BUCKET_ID,
                fileName: inputFileObject.name,
                fileSize: inputFileObject.size,
                fileType: inputFileObject.type
            });

            // Log as warning but continue with ComfyUI direct upload
            console.warn(`[GenService] ⚠️ Continuing with ComfyUI direct upload despite Nhost failure`);
            nhostUploadedInputInfo = null; // Will be handled later
        }

        // Log generation attempt with uploaded file ID (as required)
        console.log(`[GenService] 📝 Logging generation attempt with file upload info...`);
        const uploadedFileId = nhostUploadedInputInfo?.id || null;
        promptLogId = await logPromptAttempt(userId, toolId, inputFileObject.name, userPromptText, serverTypeDetermined, 'pending', uploadedFileId);

        if (!promptLogId) {
            throw new GenerationServiceError("Critical: Failed to log prompt attempt. Cannot proceed without audit trail.");
        }

        console.log(`[GenService] ✅ Generation attempt logged with ID: ${promptLogId}, Uploaded File ID: ${uploadedFileId}`);

        // Update prompt status to processing
        await updatePromptLogStatus(promptLogId, 'processing', 'Health check passed, starting generation workflow');

        // 2. Fetch ComfyUI instance details & workflow template (only if health check passed)
        console.log(`[GenService] 🔧 Fetching ComfyUI instance details and workflow template...`);
        const [workflowFileId, comfyDetails] = await Promise.all([
            getToolWorkflowId(toolId),
            getComfyInstanceDetails(toolId, serverTypeDetermined)
        ]);
        const comfyInstanceUrl = comfyDetails.url;
        serverTypeActuallyUsed = comfyDetails.server_type;
        const workflowTemplate = await getWorkflowTemplate(workflowFileId);
        const workflowPayload = JSON.parse(JSON.stringify(workflowTemplate));

        console.log(`[GenService] ✅ ComfyUI details fetched. URL: ${comfyInstanceUrl}, Server Type: ${serverTypeActuallyUsed}`);

        // 3. Upload input file DIRECTLY to ComfyUI (only if health check passed)
        console.log(`[GenService] 🚀 Uploading input file directly to ComfyUI: ${comfyInstanceUrl}`);
        comfyDirectInputInfo = await uploadInputToComfyDirect(inputFileObject, comfyInstanceUrl);
        console.log(`[GenService] ✅ Input file uploaded to ComfyUI successfully: ${comfyDirectInputInfo.name}`);

        // 6. Submit workflow to ComfyUI
        const comfySubmittedPromptId = await submitWorkflowToComfy(workflowPayload, comfyInstanceUrl, comfyDirectInputInfo.name, comfyInputImageNodeId, userPromptText, comfyInputPromptNodeId, comfyPromptInputKey);

        // 7. Poll ComfyUI for output & upload outputs to Nhost `user_outputs`
        const pollResult = await pollComfyOutputAndUploadToNhost(comfySubmittedPromptId, comfyInstanceUrl, userId, toolId, promptLogId);

        // 8. Calculate Cost
        const durationMs = pollResult.durationMs;
        const calculatedCost = await calculateCreditCost(durationMs, serverTypeActuallyUsed, toolId);

        // Update prompt log with the calculated cost
  await nhost.graphql.request(`
    mutation UpdatePromptCost($promptId: uuid!, $cost: Int!, $duration: Int!) {
      update_prompts_by_pk(
        pk_columns: { id: $promptId },
        _set: {
          credit_cost: $cost,
          generation_duration_ms: $duration,
          status: "completed"
        }
      ) {
        id
      }
    }
  `, {
    promptId: promptLogId,
    cost: calculatedCost,
    duration: durationMs
  });

        // 9. Update prompt log status to 'completed'
        await updatePromptLogStatus(promptLogId, 'completed');

        // 10. Return structured data (handle case where Nhost upload failed)
        const nhostInputFileData = nhostUploadedInputInfo ? {
            fileName: inputFileObject.name,
            nhostUrl: nhostUploadedInputInfo.url,
            nhostFileId: nhostUploadedInputInfo.id,
            contentType: nhostUploadedInputInfo.mimeType,
        } : {
            fileName: inputFileObject.name,
            nhostUrl: null, // Nhost upload failed
            nhostFileId: null,
            contentType: inputFileObject.type,
            uploadWarning: "Nhost storage upload failed, but generation completed successfully"
        };

        return {
            promptLogId,
            nhostInputFile: nhostInputFileData,
            userPromptText,
            comfyProcessingDetails: { submittedComfyPromptId, comfyInputFileName: comfyDirectInputInfo.name },
            generationOutputs: pollResult.outputs,
            usageDetails: { durationMs, calculatedCost, serverTypeUsed: serverTypeActuallyUsed, deductedFrom }
        };

    } catch (error) {
        console.error(`[GenService] 💥 RunJob V11 FAILED for tool ${toolId}:`, {
            error: error.message,
            errorType: error.name,
            originalError: error.originalError?.message || 'N/A',
            details: error.details || 'N/A',
            userId,
            toolId,
            fileName: inputFileObject?.name,
            serverType: serverTypeActuallyUsed,
            healthCheckPassed: healthCheckResults?.overall || false,
            healthCheckDetails: healthCheckResults ? {
                databaseAccess: healthCheckResults.databaseAccess.passed,
                toolValidation: healthCheckResults.toolValidation.passed,
                activeServers: healthCheckResults.activeServers.passed
            } : 'Not performed',
            nhostUploadSuccess: !!nhostUploadedInputInfo,
            comfyUploadSuccess: !!comfyDirectInputInfo,
            promptLogId
        });

        const finalErrorMessage = error.message || "An unknown generation error occurred.";

        // Always update prompt status to failed if we have a promptLogId
        if (promptLogId) {
            console.log(`[GenService] 📝 Updating prompt ${promptLogId} status to failed`);
            await updatePromptLogStatus(promptLogId, 'failed', finalErrorMessage);
        } else {
            // If we don't have a promptLogId, try to create one for the failure
            console.log(`[GenService] 📝 Creating failure log entry for early failure`);
            const failureLogId = await logPromptAttempt(userId, toolId, inputFileObject?.name, userPromptText, serverTypeActuallyUsed, 'failed');
            if (failureLogId) {
                await updatePromptLogStatus(failureLogId, 'failed', `Pre-generation Error: ${finalErrorMessage}`);
            }
        }

        throw error;
    }
}

async function saveProjectMetadata(projectDataForSave) {
    const requiredFields = ['userId', 'promptInputText', 'outputs'];
    const missingField = requiredFields.find(field => !(field in projectDataForSave) || projectDataForSave[field] === null || projectDataForSave[field] === undefined);
    if (missingField) throw new GenerationServiceError(`Save failed: Missing required field '${missingField}'.`);

    // Handle outputs - can be array of URLs or array of objects with url property
    let outputUrls = [];

    console.log("[GenService] Processing outputs:", projectDataForSave.outputs);

    if (Array.isArray(projectDataForSave.outputs)) {
        outputUrls = projectDataForSave.outputs.map(out => {
            if (typeof out === 'string') return out;
            if (out && typeof out.url === 'string') return out.url;
            if (out && typeof out.nhostFileUrl === 'string') return out.nhostFileUrl;
            if (out && typeof out.outputUrl === 'string') return out.outputUrl;
            return null;
        }).filter(url => url !== null);
    } else if (projectDataForSave.outputs && typeof projectDataForSave.outputs === 'object') {
        // Handle case where outputs is an object instead of array
        if (typeof projectDataForSave.outputs.url === 'string') {
            outputUrls.push(projectDataForSave.outputs.url);
        } else if (typeof projectDataForSave.outputs.nhostFileUrl === 'string') {
            outputUrls.push(projectDataForSave.outputs.nhostFileUrl);
        } else if (typeof projectDataForSave.outputs.outputUrl === 'string') {
            outputUrls.push(projectDataForSave.outputs.outputUrl);
        }
    }

    // Also check for direct output URL fields in the project data
    if (outputUrls.length === 0) {
        if (projectDataForSave.outputUrl && typeof projectDataForSave.outputUrl === 'string') {
            outputUrls.push(projectDataForSave.outputUrl);
        } else if (projectDataForSave.nhostFileUrl && typeof projectDataForSave.nhostFileUrl === 'string') {
            outputUrls.push(projectDataForSave.nhostFileUrl);
        }
    }

    console.log("[GenService] Extracted output URLs:", outputUrls);

    if (outputUrls.length === 0) {
        console.error("[GenService] No valid output URLs found in project data:", {
            outputs: projectDataForSave.outputs,
            outputUrl: projectDataForSave.outputUrl,
            nhostFileUrl: projectDataForSave.nhostFileUrl
        });
        throw new GenerationServiceError("Save failed: No valid output URLs found.");
    }

    try {
        const variables = {
            userId: projectDataForSave.userId,
            promptId: projectDataForSave.promptId || null,
            name: projectDataForSave.name || projectDataForSave.originalFileName || null,
            inputFileUrl: projectDataForSave.inputFileUrl || projectDataForSave.outputUrl || null,
            promptInputText: projectDataForSave.promptInputText || projectDataForSave.promptUsed || 'Generated content',
            creditCost: Number(projectDataForSave.creditCost || projectDataForSave.credit_cost || 0),
            generationDurationMs: Math.round(projectDataForSave.generationDurationMs || projectDataForSave.generation_duration_ms || 0),
            outputs: outputUrls,
        };

        console.log("[GenService] Saving project with variables:", variables);

        const { data, error } = await nhost.graphql.request(INSERT_PROJECT_MUTATION, variables);
        if (error) {
            console.error("[GenService] GraphQL error saving project:", JSON.stringify(error, null, 2));
            throw new GraphQLServiceError("Failed to save project metadata", error.response?.errors, error);
        }
        const projectId = data?.insert_projects_one?.id;
        if (!projectId) throw new GenerationServiceError("Save project mutation returned no ID.");
        console.log("[GenService] Project metadata saved, ID:", projectId);
        return projectId;
    } catch (err) {
        if (err instanceof GenerationServiceError || err instanceof GraphQLServiceError) throw err;
        console.error("[GenService] Unexpected error saving project:", err);
        throw new GenerationServiceError(`Unexpected error saving project: ${err.message}`, err);
    }
}

// --- Bucket Validation Function ---
async function validateBucketConfiguration() {
    console.log('[GenService] Validating bucket configuration...');

    const bucketValidation = {
        user_inputs: { id: USER_INPUTS_BUCKET_ID, valid: false, error: null },
        user_outputs: { id: USER_OUTPUTS_BUCKET_ID, valid: false, error: null },
        sauces: { id: SAUCES_BUCKET_ID, valid: false, error: null }
    };

    try {
        // Test file upload to user_inputs bucket
        const testBlob = new Blob(['test'], { type: 'text/plain' });
        const testFile = new File([testBlob], 'test.txt', { type: 'text/plain' });

        try {
            const testUpload = await nhost.storage.upload({
                bucketId: USER_INPUTS_BUCKET_ID,
                file: testFile,
                name: `test_${Date.now()}.txt`
            });

            if (testUpload.fileMetadata?.id) {
                bucketValidation.user_inputs.valid = true;
                console.log(`[GenService] ✅ ${USER_INPUTS_BUCKET_ID} bucket is accessible`);

                // Clean up test file
                await nhost.storage.delete({ fileId: testUpload.fileMetadata.id });
            }
        } catch (error) {
            bucketValidation.user_inputs.error = error.message;
            console.error(`[GenService] ❌ ${USER_INPUTS_BUCKET_ID} bucket validation failed:`, error.message);
        }

        // Validate sauces bucket by checking if we can get presigned URLs
        // (We can't upload to it as it's for workflow templates)
        bucketValidation.sauces.valid = true; // Assume valid if no specific test needed
        console.log(`[GenService] ✅ ${SAUCES_BUCKET_ID} bucket configuration noted`);

        // user_outputs bucket validation would be similar to user_inputs
        bucketValidation.user_outputs.valid = true; // Will be tested during actual generation
        console.log(`[GenService] ✅ ${USER_OUTPUTS_BUCKET_ID} bucket configuration noted`);

    } catch (error) {
        console.error('[GenService] Bucket validation failed:', error);
    }

    return bucketValidation;
}

// --- Function to update prompt with project ID ---
async function updatePromptProject(promptLogId, projectId) {
    if (!promptLogId || !projectId) {
        console.warn('[GenService] updatePromptProject: promptLogId or projectId missing.');
        return false;
    }

    console.log(`[GenService] 🔗 Updating prompt ${promptLogId} with project ID: ${projectId}`);

    try {
        const { data, error } = await nhost.graphql.request(UPDATE_PROMPT_PROJECT_MUTATION, {
            promptLogId,
            projectId
        });

        if (error) {
            console.error(`[GenService] ❌ GQL error updating prompt project:`, JSON.stringify(error, null, 2));
            return false;
        }

        if (data?.update_prompts_by_pk) {
            console.log(`[GenService] ✅ Prompt ${promptLogId} linked to project ${projectId}`);
            return true;
        } else {
            console.warn(`[GenService] ⚠️ Prompt project update did not return expected data`);
            return false;
        }
    } catch (err) {
        console.error(`[GenService] 💥 Unexpected error updating prompt project:`, err);
        return false;
    }
}

// --- Helper function to ensure tools exist ---
async function ensureToolExists(toolId) {
    try {
        const { data, error } = await nhost.graphql.request(HEALTH_CHECK_TOOLS_QUERY, { toolId });
        if (error) {
            console.error(`[GenService] Error checking tool ${toolId}:`, error);
            return false;
        }

        const toolExists = !!data?.tools_by_pk;
        if (!toolExists) {
            console.warn(`[GenService] Tool '${toolId}' does not exist in tools table. This may cause foreign key violations.`);
        }
        return toolExists;
    } catch (err) {
        console.error(`[GenService] Unexpected error checking tool ${toolId}:`, err);
        return false;
    }
}

// --- Export Service ---
export const GenerationService = {
    runGenerationJob,
    saveProjectMetadata,
    validateBucketConfiguration,
    performHealthCheck,
    updatePromptProject,
    ensureToolExists,
    // Export bucket IDs for verification
    bucketIds: {
        USER_INPUTS: USER_INPUTS_BUCKET_ID,
        USER_OUTPUTS: USER_OUTPUTS_BUCKET_ID,
        SAUCES: SAUCES_BUCKET_ID
    }
};
